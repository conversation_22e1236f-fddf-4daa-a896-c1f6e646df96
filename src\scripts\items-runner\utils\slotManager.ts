import { Redis } from '../../../api/utils/redis'
import { log } from '../../../api/utils/utils'
import { Player } from '../../../api/wrappers/player'
import { WorldHopping } from '../../../api/game/worldHopping'

export class SlotManager {
    private currentSlotHost: string | null = null
    private lastManageSlots: number = 0
    private readonly taskType: string

    // Cache for hasSlot to avoid Redis calls on every onDraw
    private hasSlotCache: boolean = false
    private lastHasSlotCheck: number = 0
    private readonly hasSlotCacheTimeout = 5000 // 5 seconds cache

    // Cache for slot info including world
    private currentSlotInfo: any = null

    constructor(taskType: string) {
        this.taskType = taskType
    }

    /**
     * Main slot management method to be called periodically
     * Limited to run every 10 seconds
     */
    public manageSlots(): void {
        const now = Date.now()

        // Limit execution to every 10 seconds
        if (now - this.lastManageSlots < 10000) {
            return
        }
        this.lastManageSlots = now

        // Try to take a slot if we don't have one
        if (!this.checkSlotInternal()) {
            this.tryTakeSlot()
        } else {
            // Send heartbeat and check host availability
            this.sendHeartbeat()
            this.checkHostAvailability()
        }
    }

    /**
     * Invalidate the hasSlot cache (call when slot status might have changed)
     */
    private invalidateSlotCache(): void {
        this.lastHasSlotCheck = 0
        this.hasSlotCache = false
    }

    /**
     * Check if we currently have a slot (cached to avoid Redis calls on every onDraw)
     */
    public hasSlot(): boolean {
        const now = Date.now()

        // Use cached result if it's still valid
        if (now - this.lastHasSlotCheck < this.hasSlotCacheTimeout) {
            return this.hasSlotCache
        }

        // Update cache
        this.lastHasSlotCheck = now
        this.hasSlotCache = this.checkSlotInternal()
        return this.hasSlotCache
    }

    /**
     * Internal method to actually check slot status
     */
    private checkSlotInternal(): boolean {
        if (!this.currentSlotHost) return false

        try {
            const slaveUsername = Player.local?.username
            if (!slaveUsername) return false

            const slotKey = `delivery:slots:${this.taskType}:${this.currentSlotHost}:${slaveUsername}`
            const keys = Redis.keys(slotKey)
            return keys.length > 0
        } catch (e) {
            log('[Slot Management] Error checking slot:', e)
            return false
        }
    }

    /**
     * Get the current slot host
     */
    public getCurrentSlotHost(): string | null {
        return this.currentSlotHost
    }

    /**
     * Get the current slot's world (if available)
     */
    public getCurrentSlotWorld(): number | null {
        return this.currentSlotInfo?.world || null
    }

    public getCurrentSlotInfo() {
        return this.currentSlotInfo
    }

    /**
     * Try to take an available slot
     */
    private tryTakeSlot(): void {
        try {
            const slaveUsername = Player.local?.username
            if (!slaveUsername) return

            // Get all available hosts with their info
            const availableHostsInfo = this.getAvailableHostsWithInfo()

            for (const hostInfo of availableHostsInfo) {
                const slotKey = `delivery:slots:${this.taskType}:${hostInfo.username}:${slaveUsername}`

                // Try to take the slot with TTL of 60 seconds
                const success = Redis.setnx(slotKey, slaveUsername, 60)

                if (success) {
                    this.currentSlotHost = hostInfo.username
                    this.currentSlotInfo = hostInfo.slotInfo
                    this.invalidateSlotCache()
                    log(`[Slot Management] Successfully took slot for host: ${hostInfo.username}`)

                    // Hop to the host's world if specified
                    if (hostInfo.slotInfo.world) {
                        log(`[Slot Management] Hopping to world: ${hostInfo.slotInfo.world}`)
                        WorldHopping.switchToWorld(hostInfo.slotInfo.world)
                    }

                    return
                }
            }

            log('[Slot Management] No available slots found')
        } catch (e) {
            log('[Slot Management] Error taking slot:', e)
        }
    }

    /**
     * Get list of available hosts with free slots and their info
     */
    private getAvailableHostsWithInfo(): Array<{username: string, slotInfo: any}> {
        try {
            // Get all slot-info keys for this task type
            const pattern = `delivery:slots-info:${this.taskType}:*`
            const keys = Redis.keys(pattern)

            log("Slot infos: [" + keys.join(', ') + ']')
            const availableHosts: Array<{username: string, slotInfo: any}> = []

            for (const key of keys) {

                // Extract host username from key
                const hostUsername = key.split(':').pop()
                if (!hostUsername) continue

                // Check if this host has available slots
                const slotInfo = Redis.getJson(key)
                if (!slotInfo) continue

                const maxSlots = slotInfo.maxSlots || 1

                // Count current slots taken for this host
                const currentSlotsPattern = `delivery:slots:${this.taskType}:${hostUsername}:*`
                const currentSlots = Redis.keys(currentSlotsPattern).length

                if (currentSlots < maxSlots) {
                    availableHosts.push({
                        username: hostUsername,
                        slotInfo: slotInfo
                    })
                }
            }

            return availableHosts
        } catch (e) {
            log('[Slot Management] Error getting available hosts:', e)
            return []
        }
    }

    /**
     * Send heartbeat to maintain slot
     */
    private sendHeartbeat(): void {
        if (!this.currentSlotHost) return

        try {
            const slaveUsername = Player.local?.username
            if (!slaveUsername) return

            const slotKey = `delivery:slots:${this.taskType}:${this.currentSlotHost}:${slaveUsername}`

            // Refresh the TTL to 60 seconds
            Redis.set(slotKey, slaveUsername, 60)
            log(`[Slot Management] Heartbeat sent for host: ${this.currentSlotHost}`)
        } catch (e) {
            log('[Slot Management] Error sending heartbeat:', e)
        }
    }

    /**
     * Check if the current host is still available
     */
    private checkHostAvailability(): void {
        if (!this.currentSlotHost) return

        try {
            const slotInfoKey = `delivery:slots-info:${this.taskType}:${this.currentSlotHost}`

            const keys = Redis.keys(slotInfoKey)
            if (keys.length === 0) {
                log(`[Slot Management] Host ${this.currentSlotHost} is no longer available`)
                this.currentSlotHost = null
                this.currentSlotInfo = null
                this.invalidateSlotCache()
            }
        } catch (e) {
            log('[Slot Management] Error checking host availability:', e)
        }
    }


}
